{"name": "@gorhom/paper-onboarding", "version": "1.2.0", "description": "Paper Onboarding is a material design UI slider for `React Native`.", "main": "lib/commonjs/index", "module": "lib/module/index", "types": "lib/typescript/index.d.ts", "react-native": "src/index", "source": "src/index", "private": false, "files": ["src", "lib"], "keywords": ["onboarding", "paper", "paper onboarding", "react-native", "ios", "android", "reanimated"], "repository": "https://github.com/gorhom/react-native-paper-onboarding", "author": "<PERSON> (https://gorhom.dev)", "license": "MIT", "bugs": {"url": "https://github.com/gorhom/react-native-paper-onboarding/issues"}, "homepage": "https://github.com/gorhom/react-native-paper-onboarding", "scripts": {"typescript": "tsc --noEmit", "lint": "eslint \"**/*.{js,ts,tsx}\"", "build": "bob build && yarn copy-dts && yarn delete-dts.js", "copy-dts": "copyfiles -u 1 \"src/**/*.d.ts\" lib/typescript", "delete-dts.js": "find ./lib/commonjs -name '*.d.js*' -delete && find ./lib/module -name '*.d.js*' -delete", "example": "yarn --cwd example", "bootstrap": "yarn example && yarn && cd example/ios && pod install", "release": "release-it"}, "dependencies": {"lodash.isequal": "^4.5.0", "react-native-svg": "15.11.2"}, "devDependencies": {"@commitlint/cli": "^19.0.0", "@commitlint/config-conventional": "^19.0.0", "@react-native-community/eslint-config": "^3.2.0", "@release-it/conventional-changelog": "^8.0.0", "@types/react": "^19.0.0", "@types/react-native": "^0.73.0", "auto-changelog": "^2.5.0", "copyfiles": "^2.4.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "husky": "^9.0.0", "prettier": "^3.2.0", "react": "^19.0.0", "react-native": "^0.79.0", "react-native-builder-bob": "^0.30.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-svg": "15.11.2", "release-it": "^17.0.0", "typescript": "~5.8.3"}, "peerDependencies": {"react": "*", "react-native": "*", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-svg": "15.11.2"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "yarn lint && yarn typescript"}}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", "typescript"]}}