{"name": "@gorhom/paper-onboarding", "version": "1.2.0", "description": "Paper Onboarding is a material design UI slider for `React Native`.", "main": "lib/commonjs/index", "module": "lib/module/index", "types": "lib/typescript/index.d.ts", "react-native": "src/index", "source": "src/index", "private": false, "files": ["src", "lib"], "keywords": ["onboarding", "paper", "paper onboarding", "react-native", "ios", "android", "reanimated"], "repository": "https://github.com/gorhom/react-native-paper-onboarding", "author": "<PERSON> (https://gorhom.dev)", "license": "MIT", "bugs": {"url": "https://github.com/gorhom/react-native-paper-onboarding/issues"}, "homepage": "https://github.com/gorhom/react-native-paper-onboarding", "scripts": {"typescript": "tsc --noEmit", "lint": "eslint \"**/*.{js,ts,tsx}\"", "build": "bob build && yarn copy-dts && yarn delete-dts.js", "copy-dts": "copyfiles -u 1 \"src/**/*.d.ts\" lib/typescript", "delete-dts.js": "find ./lib/commonjs -name '*.d.js*' -delete && find ./lib/module -name '*.d.js*' -delete", "example": "yarn --cwd example", "bootstrap": "yarn example && yarn && cd example/ios && pod install", "release": "release-it"}, "dependencies": {"lodash.isequal": "^4.5.0", "react-native-redash": "^14.2.4", "react-native-svg": "^12.1.1"}, "devDependencies": {"@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@react-native-community/eslint-config": "^3.0.0", "@release-it/conventional-changelog": "^3.0.1", "@types/react": "^16.9.19", "@types/react-native": "0.61.10", "auto-changelog": "^2.3.0", "copyfiles": "^2.4.1", "eslint": "^7.28.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "husky": "^4.2.5", "prettier": "^2.3.1", "react": "~16.9.0", "react-native": "~0.61.5", "react-native-builder-bob": "^0.18.1", "react-native-gesture-handler": "^1.10.3", "react-native-reanimated": "^1.13.3", "react-native-svg": "^12.1.1", "release-it": "^14.9.0", "typescript": "^3.3.2"}, "peerDependencies": {"react": "*", "react-native": "*", "react-native-gesture-handler": "^1.10.3", "react-native-reanimated": "^1.13.3", "react-native-svg": "^12.1.1"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "yarn lint && yarn typescript"}}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", "typescript"]}}