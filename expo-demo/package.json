{"name": "paper-onboarding-expo-demo", "version": "1.0.0", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.0", "expo-status-bar": "~2.0.0", "react": "^19.0.0", "react-native": "^0.79.0", "react-native-reanimated": "~3.17.4", "react-native-gesture-handler": "~2.24.0", "react-native-svg": "15.11.2", "react-native-safe-area-context": "^4.12.0", "@gorhom/paper-onboarding": "file:../"}, "devDependencies": {"@babel/core": "^7.25.0", "@types/react": "^19.0.0", "typescript": "~5.8.3"}, "private": true}