{"name": "@gorhom/paper-onboarding-example", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start"}, "dependencies": {"@gorhom/showcase-template": "^2.0.4", "@react-native-community/masked-view": "^0.1.11", "@react-navigation/native": "^5.9.4", "@react-navigation/stack": "^5.14.5", "react": "^18.2.0", "react-native": "^0.73.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^3.2.0", "react-native-screens": "^3.3.0", "react-native-svg": "15.11.2"}, "devDependencies": {"@babel/core": "^7.25.0", "@babel/runtime": "^7.25.0", "@types/react": "^18.2.0", "@types/react-native": "^0.73.0", "babel-plugin-module-resolver": "^4.1.0", "metro-react-native-babel-preset": "^0.77.0", "typescript": "~5.8.3"}}