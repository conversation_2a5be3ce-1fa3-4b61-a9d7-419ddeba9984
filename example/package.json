{"name": "@gorhom/paper-onboarding-example", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start"}, "dependencies": {"@gorhom/showcase-template": "^2.0.4", "@react-native-community/masked-view": "^0.1.11", "@react-navigation/native": "^5.9.4", "@react-navigation/stack": "^5.14.5", "react": "17.0.1", "react-native": "0.64.2", "react-native-gesture-handler": "^1.10.3", "react-native-reanimated": "^1.13.3", "react-native-redash": "^14.2.4", "react-native-safe-area-context": "^3.2.0", "react-native-screens": "^3.3.0", "react-native-svg": "^12.1.1"}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/runtime": "^7.12.5", "@types/react": "^16.9.34", "@types/react-native": "^0.62.2", "babel-plugin-module-resolver": "^4.1.0", "metro-react-native-babel-preset": "^0.64.0", "typescript": "^4.3.2"}}