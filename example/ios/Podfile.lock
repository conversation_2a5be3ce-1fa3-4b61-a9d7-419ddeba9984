PODS:
  - boost-for-react-native (1.63.0)
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.64.2)
  - FBReactNativeSpec (0.64.2):
    - RCT-Folly (= 2020.01.13.00)
    - RCTRequired (= 0.64.2)
    - RCTTypeSafety (= 0.64.2)
    - React-Core (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - Flipper (0.75.1):
    - Flipper-Folly (~> 2.5)
    - Flipper-RSocket (~> 1.3)
  - Flipper-DoubleConversion (1.1.7)
  - Flipper-Folly (2.5.3):
    - boost-for-react-native
    - Flipper-DoubleConversion
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.180)
  - Flipper-Glog (0.3.6)
  - Flipper-PeerTalk (0.0.4)
  - Flipper-RSocket (1.3.1):
    - Flipper-Folly (~> 2.5)
  - FlipperKit (0.75.1):
    - FlipperKit/Core (= 0.75.1)
  - FlipperKit/Core (0.75.1):
    - Flipper (~> 0.75.1)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
  - FlipperKit/CppBridge (0.75.1):
    - Flipper (~> 0.75.1)
  - FlipperKit/FBCxxFollyDynamicConvert (0.75.1):
    - Flipper-Folly (~> 2.5)
  - FlipperKit/FBDefines (0.75.1)
  - FlipperKit/FKPortForwarding (0.75.1):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.75.1)
  - FlipperKit/FlipperKitLayoutPlugin (0.75.1):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutTextSearchable (0.75.1)
  - FlipperKit/FlipperKitNetworkPlugin (0.75.1):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.75.1):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.75.1):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.75.1):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - glog (0.3.5)
  - libevent (2.1.12)
  - OpenSSL-Universal (1.1.180)
  - RCT-Folly (2020.01.13.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
    - RCT-Folly/Default (= 2020.01.13.00)
  - RCT-Folly/Default (2020.01.13.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
  - RCTRequired (0.64.2)
  - RCTTypeSafety (0.64.2):
    - FBLazyVector (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTRequired (= 0.64.2)
    - React-Core (= 0.64.2)
  - React (0.64.2):
    - React-Core (= 0.64.2)
    - React-Core/DevSupport (= 0.64.2)
    - React-Core/RCTWebSocket (= 0.64.2)
    - React-RCTActionSheet (= 0.64.2)
    - React-RCTAnimation (= 0.64.2)
    - React-RCTBlob (= 0.64.2)
    - React-RCTImage (= 0.64.2)
    - React-RCTLinking (= 0.64.2)
    - React-RCTNetwork (= 0.64.2)
    - React-RCTSettings (= 0.64.2)
    - React-RCTText (= 0.64.2)
    - React-RCTVibration (= 0.64.2)
  - React-callinvoker (0.64.2)
  - React-Core (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/CoreModulesHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/Default (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/DevSupport (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.2)
    - React-Core/RCTWebSocket (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-jsinspector (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTBlobHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTImageHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTTextHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTWebSocket (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-CoreModules (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/CoreModulesHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-RCTImage (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-cxxreact (0.64.2):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-callinvoker (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsinspector (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - React-runtimeexecutor (= 0.64.2)
  - React-jsi (0.64.2):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-jsi/Default (= 0.64.2)
  - React-jsi/Default (0.64.2):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
  - React-jsiexecutor (0.64.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-perflogger (= 0.64.2)
  - React-jsinspector (0.64.2)
  - react-native-safe-area-context (3.2.0):
    - React-Core
  - React-perflogger (0.64.2)
  - React-RCTActionSheet (0.64.2):
    - React-Core/RCTActionSheetHeaders (= 0.64.2)
  - React-RCTAnimation (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTAnimationHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTBlob (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/RCTBlobHeaders (= 0.64.2)
    - React-Core/RCTWebSocket (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-RCTNetwork (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTImage (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTImageHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-RCTNetwork (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTLinking (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - React-Core/RCTLinkingHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTNetwork (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTNetworkHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTSettings (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTSettingsHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTText (0.64.2):
    - React-Core/RCTTextHeaders (= 0.64.2)
  - React-RCTVibration (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/RCTVibrationHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-runtimeexecutor (0.64.2):
    - React-jsi (= 0.64.2)
  - ReactCommon/turbomodule/core (0.64.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-callinvoker (= 0.64.2)
    - React-Core (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-perflogger (= 0.64.2)
  - RNCMaskedView (0.1.11):
    - React
  - RNGestureHandler (1.10.3):
    - React-Core
  - RNReanimated (1.13.3):
    - React-Core
  - RNScreens (3.3.0):
    - React-Core
    - React-RCTImage
  - RNSVG (12.1.1):
    - React
  - Yoga (1.14.0)
  - YogaKit (1.18.1):
    - Yoga (~> 1.14)

DEPENDENCIES:
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Flipper (~> 0.75.1)
  - Flipper-DoubleConversion (= 1.1.7)
  - Flipper-Folly (~> 2.5.3)
  - Flipper-Glog (= 0.3.6)
  - Flipper-PeerTalk (~> 0.0.4)
  - Flipper-RSocket (~> 1.3)
  - FlipperKit (~> 0.75.1)
  - FlipperKit/Core (~> 0.75.1)
  - FlipperKit/CppBridge (~> 0.75.1)
  - FlipperKit/FBCxxFollyDynamicConvert (~> 0.75.1)
  - FlipperKit/FBDefines (~> 0.75.1)
  - FlipperKit/FKPortForwarding (~> 0.75.1)
  - FlipperKit/FlipperKitHighlightOverlay (~> 0.75.1)
  - FlipperKit/FlipperKitLayoutPlugin (~> 0.75.1)
  - FlipperKit/FlipperKitLayoutTextSearchable (~> 0.75.1)
  - FlipperKit/FlipperKitNetworkPlugin (~> 0.75.1)
  - FlipperKit/FlipperKitReactPlugin (~> 0.75.1)
  - FlipperKit/FlipperKitUserDefaultsPlugin (~> 0.75.1)
  - FlipperKit/SKIOSNetworkPlugin (~> 0.75.1)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCMaskedView (from `../node_modules/@react-native-community/masked-view`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - boost-for-react-native
    - CocoaAsyncSocket
    - Flipper
    - Flipper-DoubleConversion
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - Flipper-RSocket
    - FlipperKit
    - libevent
    - OpenSSL-Universal
    - YogaKit

EXTERNAL SOURCES:
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCMaskedView:
    :path: "../node_modules/@react-native-community/masked-view"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost-for-react-native: 39c7adb57c4e60d6c5479dd8623128eb5b3f0f2c
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: cf9b38bf0b2d048436d9a82ad2abe1404f11e7de
  FBLazyVector: e686045572151edef46010a6f819ade377dfeb4b
  FBReactNativeSpec: 484f1e79aac515f149364c53d458e28a1c446cb7
  Flipper: d3da1aa199aad94455ae725e9f3aa43f3ec17021
  Flipper-DoubleConversion: 38631e41ef4f9b12861c67d17cb5518d06badc41
  Flipper-Folly: 755929a4f851b2fb2c347d533a23f191b008554c
  Flipper-Glog: 1dfd6abf1e922806c52ceb8701a3599a79a200a6
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  Flipper-RSocket: 127954abe8b162fcaf68d2134d34dc2bd7076154
  FlipperKit: 8a20b5c5fcf9436cac58551dc049867247f64b00
  glog: 73c2498ac6884b13ede40eda8228cb1eee9d9d62
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  OpenSSL-Universal: 1aa4f6a6ee7256b83db99ec1ccdaa80d10f9af9b
  RCT-Folly: ec7a233ccc97cc556cf7237f0db1ff65b986f27c
  RCTRequired: 6d3e854f0e7260a648badd0d44fc364bc9da9728
  RCTTypeSafety: c1f31d19349c6b53085766359caac425926fafaa
  React: bda6b6d7ae912de97d7a61aa5c160db24aa2ad69
  React-callinvoker: 9840ea7e8e88ed73d438edb725574820b29b5baa
  React-Core: b5e385da7ce5f16a220fc60fd0749eae2c6120f0
  React-CoreModules: 17071a4e2c5239b01585f4aa8070141168ab298f
  React-cxxreact: 9be7b6340ed9f7c53e53deca7779f07cd66525ba
  React-jsi: 67747b9722f6dab2ffe15b011bcf6b3f2c3f1427
  React-jsiexecutor: 80c46bd381fd06e418e0d4f53672dc1d1945c4c3
  React-jsinspector: cc614ec18a9ca96fd275100c16d74d62ee11f0ae
  react-native-safe-area-context: f0906bf8bc9835ac9a9d3f97e8bde2a997d8da79
  React-perflogger: 25373e382fed75ce768a443822f07098a15ab737
  React-RCTActionSheet: af7796ba49ffe4ca92e7277a5d992d37203f7da5
  React-RCTAnimation: 6a2e76ab50c6f25b428d81b76a5a45351c4d77aa
  React-RCTBlob: 02a2887023e0eed99391b6445b2e23a2a6f9226d
  React-RCTImage: ce5bf8e7438f2286d9b646a05d6ab11f38b0323d
  React-RCTLinking: ccd20742de14e020cb5f99d5c7e0bf0383aefbd9
  React-RCTNetwork: dfb9d089ab0753e5e5f55fc4b1210858f7245647
  React-RCTSettings: b14aef2d83699e48b410fb7c3ba5b66cd3291ae2
  React-RCTText: 41a2e952dd9adc5caf6fb68ed46b275194d5da5f
  React-RCTVibration: 24600e3b1aaa77126989bc58b6747509a1ba14f3
  React-runtimeexecutor: a9904c6d0218fb9f8b19d6dd88607225927668f9
  ReactCommon: 149906e01aa51142707a10665185db879898e966
  RNCMaskedView: 0e1bc4bfa8365eba5fbbb71e07fbdc0555249489
  RNGestureHandler: a479ebd5ed4221a810967000735517df0d2db211
  RNReanimated: 514a11da3a2bcc6c3dfd9de32b38e2b9bf101926
  RNScreens: bf59f17fbf001f1025243eeed5f19419d3c11ef2
  RNSVG: 551acb6562324b1d52a4e0758f7ca0ec234e278f
  Yoga: 575c581c63e0d35c9a83f4b46d01d63abc1100ac
  YogaKit: f782866e155069a2cca2517aafea43200b01fd5a

PODFILE CHECKSUM: bb609d970675e1dd2ad04189056f02900c62556e

COCOAPODS: 1.10.1
