/**
 * Example usage of the migrated React Native Paper Onboarding library
 * This shows how to use the library with Expo SDK 53
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import PaperOnboarding from '@gorhom/paper-onboarding';

// Sample data for the onboarding screens
const data = [
  {
    title: 'Welcome',
    description: 'Welcome to our amazing app! This library now uses Reanimated v3 for better performance.',
    backgroundColor: '#698FB8',
    image: {
      uri: 'https://example.com/welcome.png'
    }
  },
  {
    title: 'Fast Animations',
    description: 'All animations now run on the UI thread thanks to Reanimated v3 migration.',
    backgroundColor: '#6BCF7F',
    image: {
      uri: 'https://example.com/fast.png'
    }
  },
  {
    title: 'Modern Gestures',
    description: 'Gesture handling is now powered by react-native-gesture-handler v2.',
    backgroundColor: '#C69C6D',
    image: {
      uri: 'https://example.com/gestures.png'
    }
  },
  {
    title: 'Expo SDK 53',
    description: 'Fully compatible with Expo SDK 53 and React Native 0.79+.',
    backgroundColor: '#FF6B6B',
    image: {
      uri: 'https://example.com/expo.png'
    }
  }
];

export default function OnboardingExample() {
  const safeInsets = useSafeAreaInsets();

  const handleIndexChange = (index: number) => {
    console.log('Current page index:', index);
  };

  const handleOnboardingComplete = () => {
    console.log('Onboarding completed!');
    // Navigate to main app or save completion state
  };

  return (
    <View style={styles.container}>
      <PaperOnboarding
        data={data}
        safeInsets={safeInsets}
        onIndexChange={handleIndexChange}
        onCloseButtonPress={handleOnboardingComplete}
        // Optional customization props
        direction="horizontal"
        indicatorSize={40}
        indicatorBackgroundColor="#FFFFFF"
        indicatorBorderColor="#000000"
        closeButtonText="Skip"
        closeButtonTextStyle={styles.closeButtonText}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  closeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

/**
 * Key Migration Benefits:
 * 
 * 1. Performance: All animations run on UI thread (60fps guaranteed)
 * 2. Modern APIs: Uses latest Reanimated v3 and Gesture Handler v2
 * 3. Expo Compatible: Works with Expo SDK 53 out of the box
 * 4. Type Safety: Full TypeScript support with updated types
 * 5. Future Proof: Uses modern React Native patterns
 * 
 * Installation for Expo SDK 53:
 * ```bash
 * npx expo install react-native-reanimated react-native-gesture-handler react-native-svg
 * npm install @gorhom/paper-onboarding
 * ```
 * 
 * Required setup in app.json for Expo:
 * ```json
 * {
 *   "expo": {
 *     "plugins": [
 *       "react-native-reanimated/plugin"
 *     ]
 *   }
 * }
 * ```
 */
