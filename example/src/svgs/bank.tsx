import React from 'react';
import Svg, { Defs, Path, G, Mask, Use } from 'react-native-svg';
import type { SVGProps } from './types';

export const BankSVG = ({ color = '#fff' }: SVGProps) => (
  <Svg width={197} height={199} viewBox="0 0 197 199">
    <Defs>
      <Path id="prefix__a" d="M0 0h197v199H0z" />
      <Path
        d="M94.09.797c51.914 0 94 42.086 94 94 0 24.413-9.307 46.652-24.566 63.364l31.386 31.382a5.011 5.011 0 01.004 7.078 5 5 0 01-7.078-.004l-31.435-31.44c-16.587 14.697-38.407 23.62-62.311 23.62-51.914 0-94-42.086-94-94s42.086-94 94-94zm0 10c-46.39 0-84 37.61-84 84s37.61 84 84 84 84-37.61 84-84-37.61-84-84-84z"
        id="prefix__c"
      />
      <Path id="prefix__e" d="M0 0h49v11H0z" />
      <Path
        d="M.707 1.656c0-.23.047-.43.137-.59.094-.164.219-.296.37-.402.157-.102.337-.176.54-.227.207-.046.422-.074.652-.074h2.27c.332 0 .676.008 1.027.02.352.012.695.047 1.031.101.336.059.653.141.954.25.296.11.558.266.78.461.227.2.407.45.536.75.133.297.2.664.2 1.094a2.394 2.394 0 01-.567 1.55 2.495 2.495 0 01-1.469.852v.028c.363.054.695.156.992.304.293.149.543.336.75.563.203.223.363.48.477.773.113.293.168.61.168.946 0 .37-.075.734-.22 1.09a2.627 2.627 0 01-.675.94c-.3.274-.687.493-1.152.661-.469.168-1.02.254-1.66.254H2.406a3.71 3.71 0 01-.734-.066 1.106 1.106 0 01-.531-.266 1.291 1.291 0 01-.325-.574c-.074-.246-.109-.57-.109-.977v-7.46zM3.293 9.36h1.215c.39 0 .734-.02 1.031-.058.3-.04.547-.117.75-.235.2-.117.352-.28.453-.488.102-.207.153-.476.153-.808 0-.31-.06-.56-.176-.754a1.24 1.24 0 00-.496-.465 2.364 2.364 0 00-.778-.239c-.3-.042-.64-.066-1.011-.066H3.293V9.36zm0-7.351v2.906H4.34c.32 0 .62-.023.898-.066.274-.043.512-.121.707-.235.2-.113.356-.265.47-.457.108-.195.167-.437.167-.738 0-.297-.055-.535-.168-.719a1.132 1.132 0 00-.45-.43 1.979 1.979 0 00-.663-.207 5.369 5.369 0 00-.809-.054H3.293zm14.187.355l-1.613 4.711h3.254l-1.605-4.71h-.036zm4.774 6.418c.035.086.07.18.11.274.038.097.077.191.113.289.03.097.058.191.082.281.023.094.035.172.035.246 0 .18-.035.34-.102.48-.066.141-.156.258-.265.352a1.21 1.21 0 01-.395.219 1.44 1.44 0 01-.469.078c-.191 0-.355-.027-.492-.082a.96.96 0 01-.367-.254 1.926 1.926 0 01-.297-.457 9.09 9.09 0 01-.289-.695l-.352-.93h-4.144l-.3.93c-.09.281-.177.515-.267.703a1.882 1.882 0 01-.3.46.993.993 0 01-.38.247 1.47 1.47 0 01-.495.078c-.168 0-.325-.027-.473-.078a1.252 1.252 0 01-.395-.219 1.046 1.046 0 01-.363-.832c0-.074.008-.152.031-.246.024-.09.051-.184.086-.281.032-.098.07-.192.11-.29.039-.093.074-.187.11-.273l1.968-5.324c.187-.5.363-.941.531-1.324.168-.383.356-.707.559-.969.203-.266.433-.465.699-.598.262-.132.582-.203.957-.203.352 0 .652.055.902.16.25.106.473.278.672.52.203.242.395.559.574.957.18.395.38.879.602 1.457l2.004 5.324zm6.176 1.07c0 .16-.032.31-.09.45a1.182 1.182 0 01-.617.61 1.172 1.172 0 01-.902 0 1.224 1.224 0 01-.36-.247 1.153 1.153 0 01-.332-.813v-8c0-.238.035-.449.105-.636.075-.184.168-.344.29-.469a1.15 1.15 0 01.417-.289c.164-.066.336-.098.52-.098.156 0 .312.028.46.09.15.063.29.145.427.246.136.102.27.223.398.364.125.136.246.285.363.441l4.325 5.86V1.507a1.137 1.137 0 01.328-.813c.101-.101.222-.183.363-.246.137-.062.29-.09.45-.09.16 0 .312.028.448.09.141.063.262.145.364.246.101.102.183.223.246.364.062.136.09.289.09.449v7.457c0 .265-.012.523-.043.773-.028.246-.09.461-.184.649-.09.183-.223.332-.394.445-.172.113-.399.168-.68.168-.152 0-.3-.031-.445-.09a1.88 1.88 0 01-.418-.238 3.716 3.716 0 01-.391-.363c-.125-.141-.25-.29-.375-.454l-4.363-5.93v5.927zm14.203-4.82l2.832-3.715c.215-.28.426-.511.633-.687.203-.176.441-.266.707-.266a1.29 1.29 0 01.843.325c.118.101.211.222.286.359.074.137.109.285.109.445 0 .098-.02.2-.066.305-.043.105-.098.21-.164.316-.067.106-.141.207-.227.313-.086.105-.168.207-.246.297L45.152 5.28l2.547 3.2c.082.101.168.21.25.324.082.117.16.23.227.347.07.114.125.227.168.336.047.11.066.215.066.317 0 .164-.039.316-.113.46-.074.141-.176.27-.297.38-.121.109-.258.195-.414.261a1.196 1.196 0 01-.457.094c-.176 0-.34-.04-.492-.117a2.061 2.061 0 01-.426-.301 2.824 2.824 0 01-.375-.402c-.113-.149-.223-.29-.324-.434l-2.88-3.902.016 3.726c0 .203-.039.387-.109.563a1.414 1.414 0 01-.723.758c-.164.074-.343.109-.53.109-.192 0-.368-.035-.532-.11a1.344 1.344 0 01-.43-.304 1.473 1.473 0 01-.394-1.016V1.785a1.466 1.466 0 01.394-1.012c.121-.132.266-.234.43-.304.164-.074.34-.11.531-.11.18 0 .352.036.52.11.164.07.312.172.433.304.125.13.227.278.297.454a1.4 1.4 0 01.113.558l-.015 3.246z"
        id="prefix__g"
      />
    </Defs>
    <G fill="none" fillRule="evenodd">
      <G opacity={0.5}>
        <Mask id="prefix__b" fill={color}>
          <Use xlinkHref="#prefix__a" />
        </Mask>
        <G mask="url(#prefix__b)">
          <Mask id="prefix__d" fill={color}>
            <Use xlinkHref="#prefix__c" />
          </Mask>
          <Path
            fill={color}
            fillRule="nonzero"
            mask="url(#prefix__d)"
            d="M-45.918 94.797L94.09-45.211l174.863 174.863L128.945 269.66z"
          />
        </G>
      </G>
      <Path
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        d="M37 140h111M83.07 156h12M74.07 148h10M94.07 148h20"
      />
      <Path stroke={color} strokeWidth={2} d="M140 88.121V140H45V88.031" />
      <Path
        d="M52 110.004A3.004 3.004 0 0155 107c1.656 0 3 1.344 3 3.004v9.988c0 .559-.45 1.008-.992 1.008h-4.016a1 1 0 01-.992-1.008v-9.988zM127 110.004a3.004 3.004 0 013-3.004c1.656 0 3 1.344 3 3.004v9.988c0 .559-.45 1.008-.992 1.008h-4.016a1 1 0 01-.992-1.008v-9.988zM50 137.008c0-.555.457-1.008 1-1.008h83c.555 0 1 .441 1 1.008v1.984c0 .555-.457 1.008-1 1.008H51c-.555 0-1-.441-1-1.008v-1.984z"
        stroke={color}
        strokeWidth={2}
      />
      <Path
        d="M55 133.008c0-.555.441-1.008 1.004-1.008h72.992a1 1 0 011.004 1.008v1.984c0 .555-.441 1.008-1.004 1.008H56.004A1 1 0 0155 134.992v-1.984z"
        stroke={color}
        strokeWidth={2}
      />
      <Path
        d="M62 130c0-1.105.895-2 1.992-2h11.016A1.99 1.99 0 0177 130c0 1.105-.895 2-1.992 2H63.992A1.99 1.99 0 0162 130zM62 90c0-1.105.895-2 1.992-2h11.016A1.99 1.99 0 0177 90c0 1.105-.895 2-1.992 2H63.992A1.99 1.99 0 0162 90z"
        stroke={color}
        strokeWidth={2}
      />
      <Path
        d="M65 93c0-.55.445-1 .996-1h7.008c.55 0 .996.457.996 1v34c0 .55-.445 1-.996 1h-7.008c-.55 0-.996-.457-.996-1V93zM85 130c0-1.105.895-2 1.992-2h11.016a1.99 1.99 0 011.992 2c0 1.105-.895 2-1.992 2H86.992A1.99 1.99 0 0185 130zM85 90c0-1.105.895-2 1.992-2h11.016A1.99 1.99 0 01100 90c0 1.105-.895 2-1.992 2H86.992A1.99 1.99 0 0185 90z"
        stroke={color}
        strokeWidth={2}
      />
      <Path
        d="M88 93c0-.55.445-1 .996-1h7.008c.55 0 .996.457.996 1v34c0 .55-.445 1-.996 1h-7.008c-.55 0-.996-.457-.996-1V93zM108 130c0-1.105.895-2 1.992-2h11.016a1.99 1.99 0 011.992 2c0 1.105-.895 2-1.992 2h-11.016a1.99 1.99 0 01-1.992-2zM108 90c0-1.105.895-2 1.992-2h11.016A1.99 1.99 0 01123 90c0 1.105-.895 2-1.992 2h-11.016A1.99 1.99 0 01108 90z"
        stroke={color}
        strokeWidth={2}
      />
      <Path
        d="M111 93c0-.55.445-1 .996-1h7.008c.55 0 .996.457.996 1v34c0 .55-.445 1-.996 1h-7.008c-.55 0-.996-.457-.996-1V93zM42 82.992c0-.547.45-.992 1-.992h99a1 1 0 011 .992v4.016c0 .547-.45.992-1 .992H43a1 1 0 01-1-.992v-4.016zM116 51.387c-2.7-10.18-11.977-17.684-23.004-17.684-11.016 0-20.285 7.484-22.996 17.645"
        stroke={color}
        strokeWidth={2}
      />
      <Path
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        d="M52 102h6M127 102h6"
      />
      <Path
        d="M55 57.992A.994.994 0 0156.004 57h72.992c.555 0 1.004.45 1.004.992v4.016a.994.994 0 01-1.004.992H56.004c-.555 0-1.004-.45-1.004-.992v-4.016z"
        stroke={color}
        strokeWidth={2}
      />
      <Path
        d="M67 51.992c0-.547.45-.992.992-.992h50.016c.547 0 .992.45.992.992v4.016c0 .547-.45.992-.992.992H67.992a.996.996 0 01-.992-.992v-4.016zM58 63.996c0-.55.441-.996 1.004-.996h66.992c.555 0 1.004.45 1.004.996v17.008c0 .55-.441.996-1.004.996H59.004c-.555 0-1.004-.45-1.004-.996V63.996z"
        stroke={color}
        strokeWidth={2}
      />
      <G transform="translate(68 67)">
        <Mask id="prefix__f" fill={color}>
          <Use xlinkHref="#prefix__e" />
        </Mask>
        <G mask="url(#prefix__f)">
          <Mask id="prefix__h" fill={color}>
            <Use xlinkHref="#prefix__g" />
          </Mask>
          <Path
            fill={color}
            fillRule="nonzero"
            mask="url(#prefix__h)"
            d="M-4.293-4.64H53.41V16H-4.293z"
          />
        </G>
      </G>
    </G>
  </Svg>
);
