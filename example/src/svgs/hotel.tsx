import React from 'react';
import Svg, { Defs, Path, G, Mask, Use } from 'react-native-svg';
import type { SVGProps } from './types';

export const HotelSVG = ({ color = '#fff' }: SVGProps) => (
  <Svg width={197} height={199} viewBox="0 0 197 199">
    <Defs>
      <Path id="prefix__a" d="M0 0h197v199H0z" />
      <Path
        d="M94.09.797c51.914 0 94 42.086 94 94 0 24.413-9.307 46.652-24.566 63.364l31.386 31.382a5.011 5.011 0 01.004 7.078 5 5 0 01-7.078-.004l-31.435-31.44c-16.587 14.697-38.407 23.62-62.311 23.62-51.914 0-94-42.086-94-94s42.086-94 94-94zm0 10c-46.39 0-84 37.61-84 84s37.61 84 84 84 84-37.61 84-84-37.61-84-84-84z"
        id="prefix__c"
      />
      <Path id="prefix__e" d="M0 0h57v12H0z" />
      <Path
        d="M6.848 1.785c0-.2.035-.387.109-.558.074-.176.172-.325.293-.454.121-.132.266-.234.43-.304.16-.074.34-.11.527-.11.191 0 .371.036.531.11.164.07.309.172.434.304.125.13.223.278.293.454.07.171.105.359.105.558V9.57c0 .203-.035.387-.105.563a1.414 1.414 0 01-.727.758c-.16.074-.34.109-.531.109a1.25 1.25 0 01-.527-.11 1.344 1.344 0 01-.43-.304 1.473 1.473 0 01-.395-1.016l-.007-3.023H3.074V9.57c0 .203-.035.387-.105.563a1.414 1.414 0 01-.727.758c-.164.074-.34.109-.531.109a1.281 1.281 0 01-.957-.414A1.473 1.473 0 01.355 9.57V1.785c0-.2.04-.387.11-.558.07-.176.168-.325.289-.454A1.281 1.281 0 011.71.36c.191 0 .367.036.531.11.164.07.309.172.434.304.125.13.222.278.293.454.07.171.105.359.105.558v2.676h3.774V1.785zm9.117 3.906c0 1.211.195 2.125.578 2.743.387.617.988.925 1.812.925.805 0 1.403-.308 1.801-.921.395-.618.594-1.532.594-2.747 0-.586-.05-1.109-.145-1.566-.093-.457-.238-.84-.437-1.152a1.97 1.97 0 00-.742-.715c-.301-.164-.656-.246-1.07-.246-.41 0-.766.082-1.067.246-.3.164-.55.402-.746.715-.195.312-.34.695-.434 1.152-.097.457-.144.98-.144 1.566zm-2.848 0c0-.808.11-1.55.324-2.234a4.835 4.835 0 01.98-1.766A4.444 4.444 0 0116.056.535c.652-.281 1.418-.418 2.3-.418.852 0 1.606.133 2.262.403.653.27 1.2.648 1.64 1.136.438.492.77 1.078.997 1.762.223.684.336 1.441.336 2.273 0 .813-.11 1.563-.328 2.247a4.858 4.858 0 01-.989 1.761 4.509 4.509 0 01-1.64 1.149c-.656.273-1.414.41-2.278.41-.847 0-1.593-.133-2.246-.399a4.448 4.448 0 01-1.644-1.12 4.821 4.821 0 01-1.008-1.759c-.227-.683-.34-1.445-.34-2.289zm13.66-3.394a1.067 1.067 0 01-.965-.59.906.906 0 010-.754 1.052 1.052 0 01.965-.594h6.688a1.046 1.046 0 01.734.285.93.93 0 01.227.31.854.854 0 01.086.378.825.825 0 01-.086.375.914.914 0 01-.227.305 1.046 1.046 0 01-.734.285H31.5V9.57c0 .203-.035.387-.105.563a1.414 1.414 0 01-.727.758c-.16.074-.34.109-.531.109a1.25 1.25 0 01-.528-.11 1.344 1.344 0 01-.43-.304 1.473 1.473 0 01-.394-1.016V2.297h-2.008zM39.11 11c-.246 0-.457-.05-.625-.152a1.25 1.25 0 01-.406-.403 1.677 1.677 0 01-.219-.55 3.014 3.014 0 01-.066-.618v-6.98c0-.277.016-.535.05-.77.036-.238.11-.441.227-.613s.29-.305.516-.402c.223-.098.523-.149.906-.149h4.703a1.05 1.05 0 01.723.285.96.96 0 01.223.305.916.916 0 010 .742.988.988 0 01-.547.5c-.125.051-.258.075-.399.075h-3.773v2.375h3.441c.145 0 .278.023.403.07.12.05.23.117.324.207a.893.893 0 01.219.305.842.842 0 01.082.367.917.917 0 01-.3.672 1.023 1.023 0 01-.325.199c-.125.05-.258.074-.403.074h-3.441v2.563h3.957c.129 0 .254.027.367.074.117.05.219.117.305.207.09.086.156.187.207.3a.98.98 0 01.07.372.98.98 0 01-.07.37.941.941 0 01-.512.5.895.895 0 01-.367.075h-5.27zm16.364-1.95a1.014 1.014 0 01.691.285 1.014 1.014 0 01.285.692 1.014 1.014 0 01-.285.688 1.014 1.014 0 01-.691.285h-5.016c-.262 0-.484-.059-.66-.172a1.271 1.271 0 01-.422-.453 2.137 2.137 0 01-.219-.64A4.582 4.582 0 0149.094 9V1.785a1.466 1.466 0 01.394-1.012 1.27 1.27 0 01.43-.304c.164-.074.34-.11.531-.11.192 0 .367.036.531.11.165.07.31.172.434.304.125.13.223.278.293.454.07.171.105.359.105.558l.008 7.266h3.653z"
        id="prefix__g"
      />
    </Defs>
    <G fill="none" fillRule="evenodd">
      <G opacity={0.5}>
        <Mask id="prefix__b" fill={color}>
          <Use xlinkHref="#prefix__a" />
        </Mask>
        <G mask="url(#prefix__b)">
          <Mask id="prefix__d" fill={color}>
            <Use xlinkHref="#prefix__c" />
          </Mask>
          <Path
            fill={color}
            fillRule="nonzero"
            mask="url(#prefix__d)"
            d="M-45.918 94.797L94.09-45.211l174.863 174.863L128.945 269.66z"
          />
        </G>
      </G>
      <Path
        d="M53 86a2 2 0 011.996-2h76.008c1.101 0 1.996.902 1.996 2v19a2 2 0 01-1.996 2H54.996A2.003 2.003 0 0153 105V86zM57 107h72v40H57zM57 84h72V48c0-2.21-1.797-4-4-4H61c-2.21 0-4 1.797-4 4v36zM62 44h62v-3c0-1.656-1.352-3-2.992-3H64.992A3 3 0 0062 41v3z"
        stroke={color}
        strokeWidth={2}
      />
      <Path
        d="M78 147h30v-26.008a1.996 1.996 0 00-1.992-1.992H79.992A1.996 1.996 0 0078 120.992V147zM93 120v26"
        stroke={color}
        strokeWidth={2}
      />
      <G transform="translate(66 90)">
        <Mask id="prefix__f" fill={color}>
          <Use xlinkHref="#prefix__e" />
        </Mask>
        <G mask="url(#prefix__f)">
          <Mask id="prefix__h" fill={color}>
            <Use xlinkHref="#prefix__g" />
          </Mask>
          <Path
            fill={color}
            fillRule="nonzero"
            mask="url(#prefix__h)"
            d="M-4.645-4.883H61.45v21.14H-4.645z"
          />
        </G>
      </G>
      <Path
        d="M67 60c0-3.313 2.691-6 6-6 3.313 0 6 2.676 6 6v13.008c0 .547-.457.992-1.004.992h-9.992A1.006 1.006 0 0167 73.008V60zM87 60c0-3.313 2.691-6 6-6 3.313 0 6 2.676 6 6v13.008c0 .547-.457.992-1.004.992h-9.992A1.006 1.006 0 0187 73.008V60zM107 60c0-3.313 2.691-6 6-6 3.313 0 6 2.676 6 6v13.008c0 .547-.457.992-1.004.992h-9.992a1.006 1.006 0 01-1.004-.992V60z"
        stroke={color}
        strokeWidth={2}
      />
      <Path
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        d="M47 147h92M82 163h12M73 155h10M93 155h20M98 133v2M88 133v2"
      />
      <Path stroke={color} strokeWidth={2} d="M58 111h70" />
    </G>
  </Svg>
);
