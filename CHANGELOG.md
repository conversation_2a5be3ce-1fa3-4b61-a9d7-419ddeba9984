### Changelog

All notable changes to this project will be documented in this file. Dates are displayed in UTC.

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

#### [v1.2.0](https://github.com/gorhom/react-native-paper-onboarding/compare/v1.1.0...v1.2.0)

- feat: added next & previous methods [`#34`](https://github.com/gorhom/react-native-paper-onboarding/pull/34)
- feat: added RTL support (#32) by @aleppos [`43a61e4`](https://github.com/gorhom/react-native-paper-onboarding/commit/43a61e410b3599e3666f79a758c6cab5bb8ae738)
- chore: updated build script [`1a9c307`](https://github.com/gorhom/react-native-paper-onboarding/commit/1a9c307e63dabc503be3787d0e11619a78d46601)

#### [v1.1.0](https://github.com/gorhom/react-native-paper-onboarding/compare/v1.0.0...v1.1.0)

> 16 June 2021

- docs: updated readme.md [`#25`](https://github.com/gorhom/react-native-paper-onboarding/pull/25)
- chore: updated dependencies [`57b497f`](https://github.com/gorhom/react-native-paper-onboarding/commit/57b497fe2b64803a14c39ea74680451883003230)
- feat: added Reanimated v2 compatibility [`0f1eacd`](https://github.com/gorhom/react-native-paper-onboarding/commit/0f1eacdeba103d25bf51d4d859247af12869fb74)

### [v1.0.0](https://github.com/gorhom/react-native-paper-onboarding/compare/v0.5.0...v1.0.0)

> 14 June 2020

- chore: update branding [`#24`](https://github.com/gorhom/react-native-paper-onboarding/pull/24)
- feat: optimise performance and internal refactor [`#23`](https://github.com/gorhom/react-native-paper-onboarding/pull/23)
- refactor: indicators and close button [`#22`](https://github.com/gorhom/react-native-paper-onboarding/pull/22)
- feat: export page content props [`#21`](https://github.com/gorhom/react-native-paper-onboarding/pull/21)
- feat: enable dismissible pages/slides [`#20`](https://github.com/gorhom/react-native-paper-onboarding/pull/20)
- refactor: project structure, types, naming and readme file [`#19`](https://github.com/gorhom/react-native-paper-onboarding/pull/19)
- chore: added description to onIndexChange prop [`48c552a`](https://github.com/gorhom/react-native-paper-onboarding/commit/48c552ab89e70ff70d48705201eeeeae87cc1c07)
- chore: removed unused import [`c1da684`](https://github.com/gorhom/react-native-paper-onboarding/commit/c1da684bef4bc77771bb46a23f77ac57dc7011e3)
- chore: improved circle radius calculation [`8ebaad2`](https://github.com/gorhom/react-native-paper-onboarding/commit/8ebaad260c7b5c4c8a061913ec45a78756c7a25f)

#### [v0.5.0](https://github.com/gorhom/react-native-paper-onboarding/compare/v0.4.0...v0.5.0)

> 14 May 2020

- feat: added support for a custom closeButton [`#18`](https://github.com/gorhom/react-native-paper-onboarding/pull/18)
- feat: enable content interaction (#16) [`3da8695`](https://github.com/gorhom/react-native-paper-onboarding/commit/3da8695875e6c24c41c488dab2b626012899b9e6)
- chore: updated dependencies [`e568a5b`](https://github.com/gorhom/react-native-paper-onboarding/commit/e568a5bd74b7cce2dd24a3f56f58d648153454b3)
- chore: updated examples [`7f3c9ad`](https://github.com/gorhom/react-native-paper-onboarding/commit/7f3c9ad50241f2a64d58c5e099ea37e605fcd7b3)

#### [v0.4.0](https://github.com/gorhom/react-native-paper-onboarding/compare/v0.3.1...v0.4.0)

> 21 April 2020

- feat: added support for custom view [`#14`](https://github.com/gorhom/react-native-paper-onboarding/pull/14)
- chore: update react native to v0.62.2 [`#13`](https://github.com/gorhom/react-native-paper-onboarding/pull/13)
- chore: added safe insets for examples [`0f9ac79`](https://github.com/gorhom/react-native-paper-onboarding/commit/0f9ac7935ee434b8295c88c276db4954e7ee0f36)
- chore: added github issue templates [`5f6687d`](https://github.com/gorhom/react-native-paper-onboarding/commit/5f6687dc74e8f9e616f82de4eed770dc4a1969b9)
- docs: updated readme and contributing files [`50f0618`](https://github.com/gorhom/react-native-paper-onboarding/commit/50f0618f9dccc24be53ae8821af2838eb31992b3)

#### [v0.3.1](https://github.com/gorhom/react-native-paper-onboarding/compare/v0.3.0...v0.3.1)

> 4 April 2020

- fix: updated types file path [`3dca3cc`](https://github.com/gorhom/react-native-paper-onboarding/commit/3dca3ccfeec24f3e168e8db03c3371d5b3ef0ddd)

#### [v0.3.0](https://github.com/gorhom/react-native-paper-onboarding/compare/v0.2.3...v0.3.0)

> 3 April 2020

- feat: add vertical gesture support [`#9`](https://github.com/gorhom/react-native-paper-onboarding/pull/9)
- chore: exclude example filder types from distribution [`#8`](https://github.com/gorhom/react-native-paper-onboarding/pull/8)
- chore: add @gorhom/showcase-template for example [`#7`](https://github.com/gorhom/react-native-paper-onboarding/pull/7)

#### [v0.2.3](https://github.com/gorhom/react-native-paper-onboarding/compare/v0.2.2...v0.2.3)

> 3 March 2020

- fix: moved circle animation to native thread ( by @owinter86 ) [`#6`](https://github.com/gorhom/react-native-paper-onboarding/pull/6)

#### [v0.2.2](https://github.com/gorhom/react-native-paper-onboarding/compare/v0.2.1...v0.2.2)

> 1 March 2020

- docs: update readme to include peer dependencies [`f5a44fc`](https://github.com/gorhom/react-native-paper-onboarding/commit/f5a44fc65111ae07980f58a7fd4c189f0617b43d)

#### [v0.2.1](https://github.com/gorhom/react-native-paper-onboarding/compare/v0.2.0...v0.2.1)

> 1 March 2020

- docs: add changelog [`637406b`](https://github.com/gorhom/react-native-paper-onboarding/commit/637406b24a1fed892993cacc30c219f0e84a4c64)
- chore: updated package description [`a2ad2c8`](https://github.com/gorhom/react-native-paper-onboarding/commit/a2ad2c81ba54cc4f1842dfaec61e5911fa87d617)
- chore: update package to be public [`7f27066`](https://github.com/gorhom/react-native-paper-onboarding/commit/7f2706682ec8bc0bdfbe6f70da92f9111b61ff6b)

#### v0.2.0

> 1 March 2020

- docs: update readme and add preview [`#3`](https://github.com/gorhom/react-native-paper-onboarding/pull/3)
- feat: added examples [`#2`](https://github.com/gorhom/react-native-paper-onboarding/pull/2)
- feat: implement paper onboarding [`#1`](https://github.com/gorhom/react-native-paper-onboarding/pull/1)
- init project [`c8ae76d`](https://github.com/gorhom/react-native-paper-onboarding/commit/c8ae76d14b92f5591089a344fe1a723d7658b5d6)
- chore: removed yarn.lock [`8a875ae`](https://github.com/gorhom/react-native-paper-onboarding/commit/8a875ae768a5331dbb178dbccf1d7314dc574408)
- chore: add implementation [`32b43de`](https://github.com/gorhom/react-native-paper-onboarding/commit/32b43de35891bdc33cc252773cbdab96f1c4df58)
