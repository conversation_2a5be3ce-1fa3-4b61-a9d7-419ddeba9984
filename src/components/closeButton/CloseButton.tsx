import React, { useMemo, memo, useRef } from 'react';
import { Text, TouchableOpacity } from 'react-native';
import Animated, {
  useDerivedValue,
  useAnimatedStyle,
  useAnimatedProps,
} from 'react-native-reanimated';
import { styles } from './styles';
import type { CloseButtonProps } from '../../types';

export const CloseButtonComponent = ({
  data,
  safeInsets,
  animatedIndex,
  closeButton,
  closeButtonText,
  closeButtonTextStyle: textStyleOverride,
  onCloseButtonPress,
}: CloseButtonProps) => {
  const containerRef = useRef<Animated.View>(null);

  //#region animations
  const showButtonOpacityValues = useMemo(
    () =>
      data.map((item, index) =>
        index === data.length - 1 || item.showCloseButton ? 1 : 0
      ),
    [data]
  );

  const showButtonPointerEventValues = useMemo(
    () =>
      data.map((item, index) =>
        index === data.length - 1 || item.showCloseButton ? 'auto' : 'none'
      ),
    [data]
  );

  const animatedShowButtonOpacity = useDerivedValue(() => {
    const currentIndex = Math.round(animatedIndex.value);
    const clampedIndex = Math.max(0, Math.min(currentIndex, data.length - 1));
    return showButtonOpacityValues[clampedIndex] || 0;
  }, [data.length]);

  const animatedShowButtonPointerEvent = useDerivedValue(() => {
    const currentIndex = Math.round(animatedIndex.value);
    const clampedIndex = Math.max(0, Math.min(currentIndex, data.length - 1));
    return showButtonPointerEventValues[clampedIndex] || 'none';
  }, [data.length]);
  //#endregion

  //#region styles
  const containerStyle = useAnimatedStyle(
    () => ({
      ...styles.container,
      opacity: animatedShowButtonOpacity.value,
      top: safeInsets.top,
    }),
    [safeInsets.top]
  );

  const animatedProps = useAnimatedProps(() => ({
    pointerEvents: animatedShowButtonPointerEvent.value as 'auto' | 'none',
  }));

  const textStyle = useMemo(
    () => [styles.text, textStyleOverride],
    [textStyleOverride]
  );
  //#endregion
  return (
    <Animated.View
      ref={containerRef}
      animatedProps={animatedProps}
      style={containerStyle}
    >
      {closeButton ? (
        typeof closeButton === 'function' ? (
          closeButton()
        ) : (
          closeButton
        )
      ) : (
        <TouchableOpacity onPress={onCloseButtonPress}>
          <Text style={textStyle}>{closeButtonText}</Text>
        </TouchableOpacity>
      )}
    </Animated.View>
  );
};

const CloseButton = memo(CloseButtonComponent);

export default CloseButton;
