import React, { useMemo, useCallback, memo } from 'react';
import Animated, {
  interpolate,
  useDerivedValue,
  useAnimatedStyle,
} from 'react-native-reanimated';
import PageContent from '../pageContent/PageContent';
import { styles } from './styles';
import type { PageProps } from '../../types';

const PageComponent = ({
  index,
  item,
  animatedIndex,
  indicatorSize,
  titleStyle: titleStyleOverride,
  descriptionStyle: descriptionStyleOverride,
  screenDimensions,
  safeInsets,
  handleRef,
}: PageProps) => {
  //#region animation
  const animatedFocus = useDerivedValue(() => {
    return interpolate(
      animatedIndex.value,
      [index - 1, index, index + 1],
      [0, 1, 2],
      'clamp'
    );
  }, [index]);

  const animatedContentOpacity = useDerivedValue(() => {
    const focus = interpolate(
      animatedIndex.value,
      [index - 1, index, index + 1],
      [0, 1, 2],
      'clamp'
    );
    return interpolate(focus, [0.5, 1, 1.5], [0, 1, 0], 'clamp');
  }, [index]);

  const animatedContentTopPosition = useDerivedValue(() => {
    const focus = interpolate(
      animatedIndex.value,
      [index - 1, index, index + 1],
      [0, 1, 2],
      'clamp'
    );
    return interpolate(
      focus,
      [0, 1, 2],
      [screenDimensions.height / 8, 0, (screenDimensions.height / 6) * -1],
      'clamp'
    );
  }, [index, screenDimensions.height]);
  //#endregion

  //#region styles
  const contentContainerStyle = useAnimatedStyle(() => {
    return {
      ...styles.contentContainer,
      marginTop: safeInsets.top,
      marginRight: safeInsets.right,
      marginLeft: safeInsets.left,
      marginBottom: safeInsets.bottom + indicatorSize + safeInsets.bottom,
      opacity: animatedContentOpacity.value,
      transform: [{ translateY: animatedContentTopPosition.value }],
    };
  }, [safeInsets, indicatorSize]);
  const titleStyle = useMemo(
    () => [titleStyleOverride, item.titleStyle ? item.titleStyle : null],
    [item, titleStyleOverride]
  );
  const descriptionStyle = useMemo(
    () => [
      descriptionStyleOverride,
      item.descriptionStyle ? item.descriptionStyle : null,
    ],
    [item, descriptionStyleOverride]
  );
  //#endregion

  //#region memo
  const pageContentProps = useMemo(
    () => ({
      index,
      animatedFocus,
      image: item.image,
      title: item.title,
      description: item.description,
      titleStyle,
      descriptionStyle,
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [index, item, titleStyle, descriptionStyle, animatedFocus]
  );
  //#endregion

  //#region callbacks
  const handleContainerRef = useCallback(
    (ref: Animated.View | null) => {
      if (ref) {
        handleRef({ current: ref }, index);
      }
    },
    [index, handleRef]
  );
  //#endregion

  // render
  const renderContent = useCallback(() => {
    const ContentComponent: any = item.content;
    return ContentComponent ? (
      typeof ContentComponent === 'function' ? (
        ContentComponent(pageContentProps)
      ) : (
        <ContentComponent {...pageContentProps} />
      )
    ) : (
      <PageContent {...pageContentProps} />
    );
  }, [item, pageContentProps]);
  return (
    <Animated.View
      pointerEvents={index === 0 ? 'auto' : 'none'}
      ref={handleContainerRef}
      style={styles.container}
    >
      <Animated.View style={contentContainerStyle}>
        {renderContent()}
      </Animated.View>
    </Animated.View>
  );
};

const Page = memo(PageComponent);

export default Page;
