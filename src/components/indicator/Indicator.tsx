import React, { useMemo, useCallback, memo } from 'react';
import Animated, {
  interpolate,
  useDerivedValue,
  useAnimatedStyle,
  useAnimatedProps,
} from 'react-native-reanimated';
import { Svg, Circle } from 'react-native-svg';
import { styles } from './styles';
import type { IndicatorProps } from '../../types';

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const BORDER_WIDTH = 2;

const IndicatorComponent = ({
  index,
  indicatorSize,
  indicatorBackgroundColor,
  indicatorBorderColor,
  animatedIndex,
  item,
}: IndicatorProps) => {
  const radius = useMemo(() => (indicatorSize - 2) / 2, [indicatorSize]);

  //#region animation
  const animatedRadius = useDerivedValue(() => {
    return interpolate(
      animatedIndex.value,
      [index - 1, index, index + 1],
      [radius * 0.33, radius, radius * 0.33],
      'clamp'
    );
  }, [index, radius]);

  const animatedIconScale = useDerivedValue(() => {
    return interpolate(
      animatedIndex.value,
      [index - 1, index, index + 1],
      [1 * 0.33, 1, 1 * 0.33],
      'clamp'
    );
  }, [index]);

  const animatedIconOpacity = useDerivedValue(() => {
    return interpolate(
      animatedIndex.value,
      [index - 0.25, index, index + 0.25],
      [0, 1, 0],
      'clamp'
    );
  }, [index]);

  const animatedCircleFillOpacity = useDerivedValue(() => {
    return interpolate(
      animatedIndex.value,
      [index - 1, index],
      [0, 1],
      'clamp'
    );
  }, [index]);

  const animatedCircleProps = useAnimatedProps(() => ({
    r: animatedRadius.value,
    fillOpacity: animatedCircleFillOpacity.value,
  }));
  //#endregion

  //#region styles
  const containerStyle = useMemo(
    () => ({
      ...styles.container,
      ...{
        width: indicatorSize,
        height: indicatorSize,
      },
    }),
    [indicatorSize]
  );

  const iconStyle = useAnimatedStyle(
    () => ({
      ...styles.iconContainer,
      left: BORDER_WIDTH * 2,
      right: BORDER_WIDTH * 2,
      top: BORDER_WIDTH * 2,
      bottom: BORDER_WIDTH * 2,
      borderRadius: indicatorSize,
      opacity: animatedIconOpacity.value,
      transform: [{ scale: animatedIconScale.value }],
    }),
    [indicatorSize]
  );
  //#endregion

  // renders
  const renderIcon = useCallback(() => {
    if (item.icon) {
      const IconComponent: any = item.icon;
      return (
        <Animated.View style={iconStyle}>
          {typeof IconComponent === 'function' ? (
            IconComponent({
              size: indicatorSize / 2,
            })
          ) : (
            <IconComponent size={indicatorSize / 2} />
          )}
        </Animated.View>
      );
    }
    return null;
  }, [item, indicatorSize, iconStyle]);

  return (
    <Animated.View style={containerStyle}>
      <Svg
        width={indicatorSize}
        height={indicatorSize}
        viewBox={`0 0 ${indicatorSize} ${indicatorSize}`}
      >
        <AnimatedCircle
          animatedProps={animatedCircleProps}
          cx={indicatorSize / 2}
          cy={indicatorSize / 2}
          // @ts-ignore
          fill={indicatorBackgroundColor}
          stroke={indicatorBorderColor}
          strokeWidth={BORDER_WIDTH}
        />
      </Svg>
      {renderIcon()}
    </Animated.View>
  );
};
const Indicator = memo(IndicatorComponent);

export default Indicator;
