import React, { memo } from 'react';
import { I18nManager } from 'react-native';
import { Circle } from 'react-native-svg';
import Animated, {
  interpolate,
  useDerivedValue,
  useAnimatedProps,
} from 'react-native-reanimated';
import type { BackgroundCircleProps } from '../../types';

const AnimatedCircle = Animated.createAnimatedComponent(Circle);
const BackgroundCircleComponent = ({
  index,
  animatedIndex,
  color,
  extendedSize,
  bottomPosition,
  screenDimensions,
  indicatorSize,
  animatedIndicatorsContainerPosition,
}: BackgroundCircleProps) => {
  //#region variables
  //#endregion

  //#region animations
  const animatedRadius = useDerivedValue(() => {
    const focus = interpolate(
      animatedIndex.value,
      [index - 1, index, index + 1],
      [0, 1, 2],
      'clamp'
    );
    return interpolate(focus, [0, 1], [0, extendedSize], 'clamp');
  }, [index, extendedSize]);

  const animatedLeftPosition = useDerivedValue(() => {
    const basePosition =
      animatedIndicatorsContainerPosition.value + indicatorSize / 2;
    const indexOffset = I18nManager.isRTL
      ? -((index + 1) * indicatorSize)
      : index * indicatorSize;
    const rtlOffset = I18nManager.isRTL ? screenDimensions.width : 0;

    return basePosition + indexOffset + rtlOffset;
  }, [index, indicatorSize, screenDimensions.width]);

  const animatedProps = useAnimatedProps(() => ({
    r: animatedRadius.value,
    cx: animatedLeftPosition.value,
  }));
  //#endregion

  // render
  return (
    <AnimatedCircle
      animatedProps={animatedProps}
      cy={bottomPosition}
      fill={color}
    />
  );
};

const BackgroundCircle = memo(BackgroundCircleComponent);

export default BackgroundCircle;
