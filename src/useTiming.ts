import { useCallback } from 'react';
import { I18nManager } from 'react-native';
import {
  useSharedValue,
  useAnimatedReaction,
  withTiming,
  interpolate,
  Easing,
  runOnJS,
  useDerivedValue,
  SharedValue,
} from 'react-native-reanimated';
import { State } from 'react-native-gesture-handler';

interface useTimingProps {
  value: SharedValue<number>;
  animatedStaticIndex: SharedValue<number>;
  animatedOverrideIndex: SharedValue<number>;
  velocity: SharedValue<number>;
  state: SharedValue<State>;
  size: number;
  screenWidth: number;
  onIndexChange?: (index: number) => void;
}

export const useTiming = ({
  animatedStaticIndex,
  animatedOverrideIndex,
  value,
  velocity,
  state,
  size,
  screenWidth,
  onIndexChange,
}: useTimingProps) => {
  const animatedPosition = useSharedValue(0);

  // Callback to handle index changes
  const handleIndexChange = useCallback(
    (index: number) => {
      if (onIndexChange) {
        runOnJS(onIndexChange)(index);
      }
    },
    [onIndexChange]
  );

  // Derived values for clamped input
  const valueClamp = useDerivedValue(() => {
    return interpolate(
      value.value,
      [screenWidth * -1, 0, screenWidth],
      I18nManager.isRTL ? [-1, 0, 1] : [1, 0, -1],
      'clamp'
    );
  }, [screenWidth]);

  const velocityClamp = useDerivedValue(() => {
    return interpolate(
      velocity.value,
      [screenWidth * -2, 0, screenWidth * 2],
      I18nManager.isRTL ? [-0.5, 0, 0.5] : [0.5, 0, -0.5],
      'clamp'
    );
  }, [screenWidth]);

  // Animation logic
  useAnimatedReaction(
    () => state.value,
    (currentState: State, previousState: State | null) => {
      if (currentState === State.ACTIVE) {
        // Update position during active gesture
        const currentIndex = animatedStaticIndex.value;
        const clampedValue = valueClamp.value;

        // Check bounds
        const isAtStart = currentIndex === 0 && clampedValue < 0;
        const isAtEnd = currentIndex === size - 1 && clampedValue > 0;

        if (!isAtStart && !isAtEnd) {
          animatedPosition.value = currentIndex + clampedValue;
        }
      } else if (previousState === State.ACTIVE && currentState === State.END) {
        // Handle gesture end
        const currentIndex = animatedStaticIndex.value;
        const clampedValue = valueClamp.value;
        const clampedVelocity = velocityClamp.value;

        // Determine target index
        let targetIndex = currentIndex;

        const totalMovement =
          Math.abs(clampedValue) + Math.abs(clampedVelocity);

        if (totalMovement > 0.5) {
          const shouldMoveNext =
            animatedPosition.value + clampedVelocity > currentIndex;

          if (shouldMoveNext && currentIndex < size - 1) {
            targetIndex = currentIndex + 1;
          } else if (!shouldMoveNext && currentIndex > 0) {
            targetIndex = currentIndex - 1;
          }
        }

        // Animate to target
        animatedPosition.value = withTiming(
          targetIndex,
          {
            duration: 500,
            easing: Easing.out(Easing.exp),
          },
          (finished?: boolean) => {
            if (finished) {
              animatedStaticIndex.value = targetIndex;
              handleIndexChange(targetIndex);
            }
          }
        );
      }
    }
  );

  // Handle manual override
  useAnimatedReaction(
    () => animatedOverrideIndex.value,
    (newIndex: number, previousIndex: number | null) => {
      if (newIndex !== previousIndex && newIndex >= 0 && newIndex < size) {
        animatedPosition.value = withTiming(
          newIndex,
          {
            duration: 500,
            easing: Easing.out(Easing.exp),
          },
          (finished?: boolean) => {
            if (finished) {
              animatedStaticIndex.value = newIndex;
              handleIndexChange(newIndex);
            }
          }
        );
      }
    }
  );

  return animatedPosition;
};
