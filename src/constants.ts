const DEFAULT_SAFE_INSET = 50;
const DEFAULT_DIRECTION = 'horizontal';
const DEFAULT_CLOSE_BUTTON_TEXT = 'close';
const DEFAULT_CLOSE_BUTTON_CALLBACK = () => {};
const DEFAULT_INDICATOR_SIZE = 40;
const DEFAULT_INDICATOR_BACKGROUND_COLOR = '#fff';
const DEFAULT_INDICATOR_BORDER_COLOR = '#fff';

export {
  DEFAULT_SAFE_INSET,
  DEFAULT_DIRECTION,
  DEFAULT_CLOSE_BUTTON_TEXT,
  DEFAULT_CLOSE_BUTTON_CALLBACK,
  DEFAULT_INDICATOR_SIZE,
  DEFAULT_INDICATOR_BACKGROUND_COLOR,
  DEFAULT_INDICATOR_BORDER_COLOR,
};
