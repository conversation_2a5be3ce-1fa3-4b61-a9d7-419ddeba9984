#!/usr/bin/env node

/**
 * Migration Verification Script
 *
 * This script verifies that the React Native Paper Onboarding library
 * has been successfully migrated to Expo SDK 53 compatibility.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying React Native Paper Onboarding Migration...\n');

// Check if build output exists
const buildPaths = [
  'lib/commonjs/PaperOnboarding.js',
  'lib/module/PaperOnboarding.js',
  'lib/typescript/PaperOnboarding.d.ts',
];

console.log('📦 Checking build outputs...');
for (const buildPath of buildPaths) {
  if (fs.existsSync(buildPath)) {
    console.log(`✅ ${buildPath} exists`);
  } else {
    console.log(`❌ ${buildPath} missing`);
    process.exit(1);
  }
}

// Check package.json dependencies
console.log('\n📋 Checking package.json dependencies...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

const expectedDeps = {
  'react-native-reanimated': '~3.17.4',
  'react-native-gesture-handler': '~2.24.0',
  'react-native-svg': '15.11.2',
};

for (const [dep, expectedVersion] of Object.entries(expectedDeps)) {
  const actualVersion =
    packageJson.devDependencies[dep] || packageJson.peerDependencies[dep];
  if (actualVersion === expectedVersion) {
    console.log(`✅ ${dep}: ${actualVersion}`);
  } else {
    console.log(`❌ ${dep}: expected ${expectedVersion}, got ${actualVersion}`);
  }
}

// Check that react-native-redash is removed
if (
  !packageJson.dependencies['react-native-redash'] &&
  !packageJson.devDependencies['react-native-redash'] &&
  !packageJson.peerDependencies['react-native-redash']
) {
  console.log('✅ react-native-redash: removed (deprecated)');
} else {
  console.log('❌ react-native-redash: should be removed');
}

// Check built code for Reanimated v3 APIs
console.log('\n🔧 Checking for Reanimated v3 APIs in built code...');
const builtCode = fs.readFileSync('lib/commonjs/PaperOnboarding.js', 'utf8');
const timingCode = fs.readFileSync('lib/commonjs/useTiming.js', 'utf8');
const allBuiltCode = builtCode + timingCode;

const v3APIs = [
  'useSharedValue',
  'useDerivedValue',
  'useAnimatedReaction',
  'withTiming',
  'runOnJS',
  'GestureDetector',
  'Gesture.Pan',
];

for (const api of v3APIs) {
  if (allBuiltCode.includes(api)) {
    console.log(`✅ ${api}: found in built code`);
  } else {
    console.log(`❌ ${api}: missing from built code`);
  }
}

// Check that old v1 APIs are not present
console.log('\n🚫 Checking that old Reanimated v1 APIs are removed...');
const v1APIs = [
  'useCode',
  'onChange',
  '_reactNativeReanimated.call(',
  '_reactNativeReanimated.block(',
  '_reactNativeReanimated.cond(',
  '_reactNativeReanimated.set(',
  '_reactNativeReanimated.timing(',
  'usePanGestureHandler',
  'PanGestureHandler',
];

let foundOldAPIs = false;
for (const api of v1APIs) {
  if (allBuiltCode.includes(api)) {
    console.log(`❌ ${api}: old v1 API still present`);
    foundOldAPIs = true;
  } else {
    console.log(`✅ ${api}: old v1 API removed`);
  }
}

// Check TypeScript types
console.log('\n📝 Checking TypeScript types...');
const typesContent = fs.readFileSync('src/types.d.ts', 'utf8');

if (typesContent.includes('SharedValue<number>')) {
  console.log('✅ SharedValue types: updated');
} else {
  console.log('❌ SharedValue types: not updated');
}

if (!typesContent.includes('Animated.Node<number>')) {
  console.log('✅ Animated.Node types: removed');
} else {
  console.log('❌ Animated.Node types: still present');
}

// Final summary
console.log('\n' + '='.repeat(50));
if (!foundOldAPIs) {
  console.log('🎉 MIGRATION VERIFICATION PASSED! 🎉');
  console.log(
    '\nThe React Native Paper Onboarding library has been successfully migrated to:'
  );
  console.log('✅ Reanimated v3 (~3.17.4)');
  console.log('✅ Gesture Handler v2 (~2.24.0)');
  console.log('✅ React Native SVG (15.11.2)');
  console.log('✅ Expo SDK 53 compatibility');
  console.log('✅ Modern TypeScript types');
  console.log('✅ Removed deprecated dependencies');
  console.log('\n🚀 The library is ready for production use with Expo SDK 53!');
} else {
  console.log('❌ MIGRATION VERIFICATION FAILED');
  console.log('Some old APIs are still present in the code.');
  process.exit(1);
}
