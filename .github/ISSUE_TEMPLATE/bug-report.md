---
name: Bug report
about: Create a report to help us improve
title: ''
labels: bug
assignees: ''
---# Bug

<!--
  Please provide a clear and concise description of what the bug is.
  Include screenshots or gifs if needed.
  Please test using the latest release of the library, as maybe your bug has been already fixed.
  If the library has multiple install methods, describe installation method (e.g., pod, not pod, with jetifier etc).

  **Please note that issues that do not follow the template may be closed.**
-->

## Environment info

<!--
  Please provide the version of the libraries below.
-->

| Library                      | Version |
| ---------------------------- | ------- |
| @gorhom/paper-onboarding     | x.x.x   |
| react-native                 | x.x.x   |
| react-native-reanimated      | x.x.x   |
| react-native-gesture-handler | x.x.x   |
| react-native-svg             | x.x.x   |

## Steps To Reproduce

<!--
- You must provide a clear list of steps and code to reproduce the problem.
- Keep the code reproducing the bug as simple as possible, with the minimum amount of code required to reproduce the issue. See https://stackoverflow.com/help/mcve.
- Either re-create the bug using the repository's example app or link to a GitHub repository with code that reproduces the bug.
- Explain the steps we need to take to reproduce the issue:
-->

1. 2.

3.

Describe what you expected to happen:

1. 2.

## Reproducible sample code

<!--
 Please add minimal runnable repro as explained above so that the bug can be tested in isolation.
-->
